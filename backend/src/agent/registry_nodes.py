"""
Registry Nodes for LangGraph Integration

This module contains LangGraph nodes that handle plant registry operations,
UID generation, and entity extraction triggering within the workflow.
"""

import asyncio
import threading
from typing import Dict, Any, List
from agent.state import OverallState
from agent.database_manager import get_database_manager, DiscoveryStatus
from agent.utils import get_research_topic
from google.genai import Client
import os


def get_web_search_function():
    """
    Get the web search function using Google Search API implementation
    """
    genai_client = Client(api_key=os.getenv("GEMINI_API_KEY"))

    def perform_web_search(query: str):
        """
        Perform web search using Google Search API via Gemini

        Args:
            query: Search query string

        Returns:
            List of search results
        """
        try:
            # Use Google Search API through genai client
            from agent.prompts import web_searcher_instructions, get_current_date
            from agent.configuration import Configuration
            from langchain_core.runnables import RunnableConfig

            configurable = Configuration.from_runnable_config(RunnableConfig(configurable={}))
            current_date = get_current_date()

            # Format the search prompt
            formatted_prompt = web_searcher_instructions.format(
                current_date=current_date,
                research_topic=query
            )

            # Use Google Search API through genai client
            response = genai_client.models.generate_content(
                model=configurable.web_searcher_model,
                contents=formatted_prompt,
                config={
                    "tools": [{"google_search": {}}],
                    "temperature": 0,
                    "max_output_tokens": 2048,
                    "top_k": 40,
                    "top_p": 0.95,
                }
            )

            # Extract search results from response
            if response and hasattr(response, 'text'):
                results = []
                content = response.text

                # Create a comprehensive result from the search
                results.append({
                    "title": f"Search results for: {query}",
                    "content": content,
                    "url": "https://search.google.com"
                })

                return results
            else:
                return []

        except Exception as e:
            print(f"⚠️ Web search failed for query '{query}': {e}")
            return []

    return perform_web_search


def check_plant_registry(state: OverallState) -> Dict[str, Any]:
    """
    LangGraph node to check if plant exists in registry
    
    This node checks if the input plant exists in the database and
    determines the next routing step.
    
    Args:
        state: Current graph state
        
    Returns:
        Updated state with plant registry information
    """
    session_id = state.get("session_id", "unknown")
    messages = state.get("messages", [])
    
    # Extract plant name from messages
    plant_name = ""
    if messages:
        plant_name = get_research_topic(messages)
    
    if not plant_name:
        print(f"[Session {session_id}] ❌ No plant name found in messages")
        return {
            "plant_exists_in_db": False,
            "plant_registry_error": "No plant name found in messages"
        }
    
    print(f"[Session {session_id}] 🔍 Checking plant registry for: {plant_name}")
    
    # Check database
    db_manager = get_database_manager()
    plant_info = db_manager.check_plant_exists(plant_name)
    
    if plant_info:
        print(f"[Session {session_id}] ✅ Plant found in registry:")
        print(f"   Organization: {plant_info['org_name']}")
        print(f"   Country: {plant_info['country']}")
        print(f"   Org UID: {plant_info['org_uid']}")
        print(f"   Plant UID: {plant_info.get('plant_uid', 'Not set')}")
        print(f"   Status: {plant_info['plant_status']}")
        
        return {
            "plant_exists_in_db": True,
            "plant_registry_info": plant_info,
            "org_uid": plant_info["org_uid"],
            "plant_uid": plant_info.get("plant_uid"),
            "org_name": plant_info["org_name"],
            "country": plant_info["country"]
        }
    else:
        print(f"[Session {session_id}] ❌ Plant not found in registry: {plant_name}")
        return {
            "plant_exists_in_db": False,
            "plant_registry_info": None
        }


def quick_org_discovery_node(state: OverallState) -> Dict[str, Any]:
    """
    LangGraph node for quick organization discovery

    This node performs actual web search-based organization discovery to find:
    1. The real organization that owns the plant
    2. All plants owned by that organization
    3. Country and operational details

    Args:
        state: Current graph state

    Returns:
        Updated state with discovered organization plants
    """
    session_id = state.get("session_id", "unknown")
    messages = state.get("messages", [])

    # Extract plant name from messages
    plant_name = ""
    if messages:
        plant_name = get_research_topic(messages)

    print(f"[Session {session_id}] 🔍 Starting quick organization discovery for: {plant_name}")

    try:
        # Import and use the proper quick discovery function
        from agent.quick_org_discovery import perform_quick_org_discovery

        # Get web search function
        web_search_fn = get_web_search_function()

        # Perform actual organization discovery with web search
        print(f"[Session {session_id}] 🌐 Running web search-based organization discovery...")
        org_info = perform_quick_org_discovery(plant_name, web_search_fn)

        print(f"[Session {session_id}] ✅ Quick discovery complete")
        print(f"[Session {session_id}]    Organization: {org_info['org_name']}")
        print(f"[Session {session_id}]    Country: {org_info['country']}")
        print(f"[Session {session_id}]    Plants discovered: {len(org_info['plants'])}")

        # Return comprehensive discovery results
        return {
            "org_discovery_complete": True,
            "discovered_org_name": org_info["org_name"],
            "discovered_country": org_info["country"],
            "discovered_plants": org_info["plants"],
            "discovery_session_id": session_id,
            "discovered_org_info": org_info  # Full org info for later use
        }

    except Exception as e:
        error_msg = f"Quick discovery failed: {str(e)}"
        print(f"[Session {session_id}] ❌ {error_msg}")

        # Fallback to basic extraction if web search fails
        print(f"[Session {session_id}] 🔄 Falling back to basic organization extraction...")

        # Extract organization name from plant name as fallback
        org_name = plant_name
        suffixes_to_remove = [
            " Power Plant", " Power Station", " Plant", " Station",
            " Solar Plant", " Solar Farm", " Solar Park", " Solar",
            " Wind Farm", " Wind Plant", " Thermal Plant"
        ]

        for suffix in suffixes_to_remove:
            if plant_name.endswith(suffix):
                org_name = plant_name[:-len(suffix)].strip()
                break

        # Create basic fallback data
        discovered_plants = [{"name": plant_name, "status": "operational", "country": "Unknown"}]

        return {
            "org_discovery_complete": True,
            "discovered_org_name": org_name,
            "discovered_country": "Unknown",
            "discovered_plants": discovered_plants,
            "discovery_session_id": session_id,
            "discovery_error": error_msg
        }


def generate_uid_node(state: OverallState) -> Dict[str, Any]:
    """
    LangGraph node to generate organization and plant UIDs
    
    This node generates unique identifiers for organizations and plants
    and stores them in the database.
    
    Args:
        state: Current graph state
        
    Returns:
        Updated state with generated UIDs
    """
    session_id = state.get("session_id", "unknown")

    # Debug: Print entire state to see what we received
    print(f"[Session {session_id}] 🔍 DEBUG: generate_uid_node received state keys: {list(state.keys())}")
    print(f"[Session {session_id}] 🔍 DEBUG: org_discovery_complete = {state.get('org_discovery_complete')}")
    print(f"[Session {session_id}] 🔍 DEBUG: discovered_org_name = '{state.get('discovered_org_name')}'")
    print(f"[Session {session_id}] 🔍 DEBUG: discovered_country = '{state.get('discovered_country')}'")

    # Get organization info from state
    org_name = state.get("discovered_org_name", "")
    country = state.get("discovered_country", "Unknown")

    if not org_name:
        print(f"[Session {session_id}] ❌ No organization name found for UID generation")
        print(f"[Session {session_id}] ❌ DEBUG: org_name value: '{org_name}' (type: {type(org_name)})")
        return {
            "uid_generation_error": "No organization name found"
        }
    
    print(f"[Session {session_id}] 🔑 Generating UIDs for organization: {org_name}")
    
    # Generate organization UID
    db_manager = get_database_manager()
    org_uid = db_manager.generate_org_uid(org_name, country)
    
    print(f"[Session {session_id}] ✅ Generated organization UID: {org_uid}")
    
    return {
        "org_uid": org_uid,
        "uid_generation_complete": True,
        "generated_org_uid": org_uid
    }


def populate_database_async_node(state: OverallState) -> Dict[str, Any]:
    """
    LangGraph node to populate database with discovered plants
    
    This node saves the discovered organization and plants to the database
    with generated UIDs.
    
    Args:
        state: Current graph state
        
    Returns:
        Updated state with database population results
    """
    session_id = state.get("session_id", "unknown")
    
    # Get required data from state
    org_name = state.get("discovered_org_name", "")
    country = state.get("discovered_country", "Unknown")
    org_uid = state.get("org_uid", "")
    discovered_plants = state.get("discovered_plants", [])
    
    if not all([org_name, org_uid, discovered_plants]):
        print(f"[Session {session_id}] ❌ Missing required data for database population")
        return {
            "database_population_error": "Missing required data"
        }
    
    print(f"[Session {session_id}] 💾 Populating database with {len(discovered_plants)} plants")
    
    # Get input plant name for tracking
    messages = state.get("messages", [])
    input_plant_name = ""
    if messages:
        input_plant_name = get_research_topic(messages)
    
    # Save to database
    db_manager = get_database_manager()
    success = db_manager.save_organization_plants(
        org_name=org_name,
        country=country,
        plants_data=discovered_plants,
        org_uid=org_uid,
        discovery_session_id=session_id,
        discovered_from_plant=input_plant_name
    )
    
    if success:
        print(f"[Session {session_id}] ✅ Database population completed")
        return {
            "database_population_complete": True,
            "database_population_success": True
        }
    else:
        print(f"[Session {session_id}] ❌ Database population failed")
        return {
            "database_population_complete": True,
            "database_population_success": False,
            "database_population_error": "Failed to save to database"
        }


def entity_extraction_trigger_node(state: OverallState) -> Dict[str, Any]:
    """
    LangGraph node to trigger entity-level extraction

    This node starts the entity-level extraction process for all plants
    in the organization after database population is complete.

    Args:
        state: Current graph state

    Returns:
        Updated state with entity extraction trigger results
    """
    session_id = state.get("session_id", "unknown")
    discovered_plants = state.get("discovered_plants", [])

    print(f"[Session {session_id}] 🚀 Entity extraction trigger activated")
    print(f"[Session {session_id}] Plants to process: {len(discovered_plants)}")

    if len(discovered_plants) <= 1:
        print(f"[Session {session_id}] ⚠️ Only {len(discovered_plants)} plant(s) found - skipping entity extraction")
        return {
            "entity_extraction_triggered": False,
            "entity_extraction_reason": "Insufficient plants for entity extraction"
        }

    try:
        # Import and initialize entity extraction controller
        from agent.entity_extraction_controller import EntityExtractionController

        # Get the input plant name that triggered this discovery
        messages = state.get("messages", [])
        input_plant_name = ""
        if messages:
            input_plant_name = get_research_topic(messages)

        if not input_plant_name:
            print(f"[Session {session_id}] ❌ Could not determine input plant name")
            return {
                "entity_extraction_triggered": False,
                "entity_extraction_error": "Could not determine input plant name"
            }

        print(f"[Session {session_id}] 🎯 Triggering entity extraction for: {input_plant_name}")
        print(f"[Session {session_id}] 📋 Will process {len(discovered_plants)} plants")

        # SIMPLE: Create controller (org_uid is already correct in database)
        controller = EntityExtractionController(input_plant_name)

        # For now, we'll trigger it and let it run in background
        # In production, you might want to use a task queue like Celery
        import asyncio
        import threading

        def run_entity_extraction():
            """Run entity extraction in background thread"""
            try:
                # Create new event loop for this thread
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                # Run the extraction
                result = loop.run_until_complete(controller.execute_entity_extraction())
                print(f"[Session {session_id}] 🎉 Entity extraction completed: {result}")
                
            except Exception as e:
                print(f"[Session {session_id}] ❌ Entity extraction failed: {e}")
            finally:
                loop.close()

        # Start entity extraction in background
        extraction_thread = threading.Thread(target=run_entity_extraction, daemon=True)
        extraction_thread.start()

        return {
            "entity_extraction_triggered": True,
            "entity_extraction_status": "started_in_background",
            "entity_plants_count": len(discovered_plants)
        }

    except Exception as e:
        error_msg = f"Entity extraction trigger failed: {str(e)}"
        print(f"[Session {session_id}] ❌ {error_msg}")
        return {
            "entity_extraction_triggered": False,
            "entity_extraction_error": error_msg
        }


# ===== ROUTING FUNCTIONS =====

def route_after_registry_check(state: OverallState) -> str:
    """
    Routing function after plant registry check
    
    Args:
        state: Current graph state
        
    Returns:
        Next node name
    """
    session_id = state.get("session_id", "unknown")
    
    if state.get("plant_exists_in_db", False):
        return "generate_uid"  # Skip discovery, go straight to UID
    else:
        return "quick_org_discovery"  # Need to discover organization


def route_after_uid_generation(state: OverallState) -> str:
    """
    Routing function after UID generation
    
    Args:
        state: Current graph state
        
    Returns:
        Next node name
    """
    return "populate_database_async"  # Always populate database after UID generation


def route_after_database_population(state: OverallState) -> str:
    """
    Routing function after database population
    
    Decides whether to trigger entity extraction or continue with single plant processing
    
    Args:
        state: Current graph state
        
    Returns:
        Next node name
    """
    session_id = state.get("session_id", "unknown")
    discovered_plants = state.get("discovered_plants", [])

    print(f"[Session {session_id}] 🔍 Discovered plants: {len(discovered_plants)}")

    # Decision: Entity extraction vs Single plant extraction
    if len(discovered_plants) > 1:
        print(f"[Session {session_id}] 🏭 Multiple plants detected ({len(discovered_plants)} plants)")
        print(f"[Session {session_id}] ➡️  ROUTING TO: entity_extraction_trigger (start entity-level extraction)")
        return "entity_extraction_trigger"
    else:
        print(f"[Session {session_id}] 🏭 Single plant detected")
        print(f"[Session {session_id}] ➡️  ROUTING TO: spawn_parallel_processing_with_uid (start 3-level + image extraction)")
        return "spawn_parallel_processing_with_uid"
